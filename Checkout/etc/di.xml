<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Logger Configuration -->
    <virtualType name="P2b2_Checkout\Logger\P2b2Handler" type="Monolog\Handler\StreamHandler">
        <arguments>
            <argument name="stream" xsi:type="string">var/log/pb2b.log</argument>
            <argument name="level" xsi:type="const">Monolog\Logger::DEBUG</argument
        </arguments>
    </virtualType>
    <virtualType name="P2b2_Checkout\Model\Logger" type="Monolog\Logger">
        <arguments>
            <argument name="name" xsi:type="string">p2b2</argument>
            <argument name="handlers" xsi:type="array">
                <item name="pb2b" xsi:type="object">P2b2_Checkout\Logger\P2b2Handler</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Plugin for PaymentInformationManagement -->
    <type name="Magento\Checkout\Model\PaymentInformationManagement">
        <plugin name="p2b2_checkout_payment_information" type="P2b2_Checkout\Plugin\PaymentInformationManagementPlugin" sortOrder="10"/>
    </type>
</config>