<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="payment" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="p2b2_checkout" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Paybank2Bank</label>
                <field id="active" translate="label comment" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable Paybank2Bank payment method.</comment>
                </field>
                <field id="title" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Title</label>
                    <comment>Payment method title shown to customer.</comment>
                </field>
                <field id="environment" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Environment</label>
                    <source_model>P2b2_Checkout\Model\Config\Source\Environment</source_model>
                    <comment>Sandbox or Production</comment>
                </field>
                <field id="api_endpoint" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>API Endpoint</label>
                    <comment>URL for create payment, e.g. https://api.paybank2bank.io/payment/createPayment</comment>
                </field>
                <field id="api_key" translate="label comment" type="obscure" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>API Key</label>
                    <comment>x-api-key header value.</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="client_key_sandbox" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client Key (Sandbox)</label>
                    <comment>Client key for sandbox environment.</comment>
                </field>
                <field id="client_secret_sandbox" translate="label comment" type="obscure" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client Secret (Sandbox)</label>
                    <comment>Client secret for sandbox environment.</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="client_key_production" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client Key (Production)</label>
                    <comment>Client key for production environment.</comment>
                </field>
                <field id="client_secret_production" translate="label comment" type="obscure" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Client Secret (Production)</label>
                    <comment>Client secret for production environment.</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="merchant_name" translate="label comment" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Merchant Name</label>
                    <comment>Merchant name displayed on hosted pages.</comment>
                </field>
                <field id="merchant_callback_url" translate="label comment" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Merchant Callback URL</label>
                    <comment>URL to redirect customer after authorization (e.g., checkout/onepage/success).</comment>
                </field>
                <field id="beneficiary_name" translate="label comment" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Beneficiary Name</label>
                    <comment>Legal name for beneficiary account.</comment>
                </field>
                <field id="beneficiary_iban" translate="label comment" type="text" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Beneficiary IBAN</label>
                    <comment>IBAN for beneficiary.</comment>
                </field>
                <field id="debug_mode" translate="label comment" type="select" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Debug Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable debug logging (requests/responses).</comment>
                </field>
                <field id="sort_order" translate="label comment" type="text" sortOrder="140" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sort Order</label>
                    <comment>Payment method sort order.</comment>
                </field>
            </group>
        </section>
    </system>
</config>