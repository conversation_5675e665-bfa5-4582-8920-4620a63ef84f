<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <payment>
            <p2b2_checkout>
                <active>0</active>
                <title>Paybank2Bank</title>
                <environment>sandbox</environment>
                <api_endpoint>https://api.paybank2bank.io/payment/createPayment</api_endpoint>
                <merchant_name>paybank2bank.io test</merchant_name>
                <merchant_callback_url>https://yourstore.com/checkout/onepage/success</merchant_callback_url>
                <beneficiary_name>Paybank2bank Demo</beneficiary_name>
                <beneficiary_iban>************************</beneficiary_iban>
                <debug_mode>0</debug_mode>
                <sort_order>10</sort_order>
            </p2b2_checkout>
        </payment>
    </default>
</config>