<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.payment.method.list">
            <block class="Magento\Framework\View\Element\Template" name="checkout.payment.method.pb2b" template="Magento_Checkout::payment/list.phtml">
                <arguments>
                    <argument name="jsLayout" xsi:type="array">
                        <item name="components" xsi:type="array">
                            <item name="checkout" xsi:type="array">
                                <item name="children" xsi:type="array">
                                    <item name="steps" xsi:type="array">
                                        <item name="children" xsi:type="array">
                                            <item name="billing-step" xsi:type="array">
                                                <item name="children" xsi:type="array">
                                                    <item name="payment" xsi:type="array">
                                                        <item name="children" xsi:type="array">
                                                            <item name="renders" xsi:type="array">
                                                                <item name="children" xsi:type="array">
                                                                    <item name="pb2b" xsi:type="array">
                                                                        <item name="component" xsi:type="string">P2b2_Checkout/js/view/payment/pb2b</item>
                                                                        <item name="methods" xsi:type="array">
                                                                            <item name="pb2b" xsi:type="array">
                                                                                <item name="isBillingAddressRequired" xsi:type="boolean">true</item>
                                                                            </item>
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
