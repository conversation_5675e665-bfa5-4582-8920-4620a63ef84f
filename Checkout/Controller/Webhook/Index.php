<?php
namespace P2b2_Checkout\Controller\Webhook;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\RawFactory;
use Magento\Framework\Webapi\Exception as WebapiException;
use Magento\Sales\Model\ResourceModel\Order\Payment\CollectionFactory as PaymentCollectionFactory;
use Magento\Sales\Model\Order;
use P2b2_Checkout\Model\Logger;

class Index extends Action
{
    /**
     * @var Logger
     */
    protected $p2b2Logger;

    /**
     * @var PaymentCollectionFactory
     */
    protected $paymentCollectionFactory;

    /**
     * @var RawFactory
     */
    protected $resultRawFactory;

    /**
     * @param Context $context
     * @param Logger $p2b2Logger
     * @param PaymentCollectionFactory $paymentCollectionFactory
     * @param RawFactory $resultRawFactory
     */
    public function __construct(
        Context $context,
        Logger $p2b2Logger,
        PaymentCollectionFactory $paymentCollectionFactory,
        RawFactory $resultRawFactory
    ) {
        parent::__construct($context);
        $this->p2b2Logger = $p2b2Logger;
        $this->paymentCollectionFactory = $paymentCollectionFactory;
        $this->resultRawFactory = $resultRawFactory;
    }

    /**
     * Handle webhook POST
     *
     * @return \Magento\Framework\Controller\Result\Raw
     */
    public function execute()
    {
        $this->p2b2Logger->info('Webhook request received');

        $input = $this->getRequest()->getContent();
        $data = json_decode($input, true);

        if (!$data || !isset($data['pb2bId'])) {
            $this->p2b2Logger->warning('Invalid webhook payload', ['payload' => $input]);
            return $this->createResponse(400, 'Invalid payload');
        }

        $pb2bId = $data['pb2bId'];
        $pb2bStatus = $data['pb2bStatus'] ?? '';

        $this->p2b2Logger->info('Processing webhook', [
            'pb2b_id' => $pb2bId,
            'status' => $pb2bStatus,
            'payload' => $data
        ]);

        $order = $this->getOrderByPb2bId($pb2bId);
        if (!$order || !$order->getId()) {
            $this->p2b2Logger->warning('No pending order found for pb2bId', ['pb2b_id' => $pb2bId]);
            return $this->createResponse(200, 'OK');
        }

        if ($order->getState() !== Order::STATE_PENDING_PAYMENT) {
            $this->p2b2Logger->info('Order already processed', ['order_id' => $order->getId(), 'current_state' => $order->getState()]);
            return $this->createResponse(200, 'OK');
        }

        $order->setState(Order::STATE_PROCESSING);
        $order->setStatus(Order::STATUS_PROCESSING);

        if ($pb2bStatus === '4. Initiated') {
            $order->setState(Order::STATE_PROCESSING);
            $order->setStatus(Order::STATUS_PROCESSING);
        } elseif (strpos($pb2bStatus, 'Failed') !== false || strpos($pb2bStatus, '4. Failed') !== false) {
            $order->setState(Order::STATE_CANCELED);
            $order->setStatus(Order::STATUS_CANCELED);
        } else {
            $this->p2b2Logger->warning('Unknown payment status', ['status' => $pb2bStatus, 'order_id' => $order->getId()]);
            return $this->createResponse(200, 'OK');
        }

        $order->addCommentToStatusHistory(
            'Payment status updated via Paybank2Bank webhook: ' . $pb2bStatus,
            false
        );
        $order->save();

        $this->p2b2Logger->info('Order status updated via webhook', [
            'order_id' => $order->getId(),
            'new_state' => $order->getState(),
            'status' => $pb2bStatus
        ]);

        return $this->createResponse(200, 'OK');
    }

    /**
     * Retrieve order by pb2bId from payment additional info
     *
     * @param string $pb2bId
     * @return Order|null
     */
    protected function getOrderByPb2bId($pb2bId)
    {
        $collection = $this->paymentCollectionFactory->create();
        $collection->addFieldToFilter(
            'additional_information',
            ['like' => '%"pb2b_id":"' . addslashes($pb2bId) . '"%']
        );
        $payment = $collection->getFirstItem();
        return $payment->getId() ? $payment->getOrder() : null;
    }

    /**
     * Create raw response
     *
     * @param int $code
     * @param string $body
     * @return \Magento\Framework\Controller\Result\Raw
     */
    protected function createResponse($code, $body)
    {
        /** @var \Magento\Framework\Controller\Result\Raw $result */
        $result = $this->resultRawFactory->create();
        $result->setHttpResponseCode($code);
        $result->setContents($body);
        return $result;
    }
}