<?php
namespace P2b2_Checkout\Plugin;

use Magento\Checkout\Api\Data\PaymentDetailsInterface;
use Magento\Checkout\Model\PaymentInformationManagement;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\HTTP\ClientInterface;
use Magento\Store\Model\ScopeInterface;
use P2b2_Checkout\Model\Logger;
use Psr\Log\LoggerInterface as PsrLogger;

class PaymentInformationManagementPlugin
{
    const CODE = 'p2b2_checkout';

    /**
     * @var CheckoutSession
     */
    protected $checkoutSession;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var ClientInterface
     */
    protected $httpClient;

    /**
     * @var Logger
     */
    protected $p2b2Logger;

    /**
     * @param CheckoutSession $checkoutSession
     * @param ScopeConfigInterface $scopeConfig
     * @param ClientInterface $httpClient
     * @param Logger $p2b2Logger
     */
    public function __construct(
        CheckoutSession $checkoutSession,
        ScopeConfigInterface $scopeConfig,
        ClientInterface $httpClient,
        Logger $p2b2Logger
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->scopeConfig = $scopeConfig;
        $this->httpClient = $httpClient;
        $this->p2b2Logger = $p2b2Logger;
    }

    /**
     * Around plugin to create payment session before placing order
     *
     * @param PaymentInformationManagement $subject
     * @param callable $proceed
     * @param int $cartId
     * @param string $email
     * @param PaymentDetailsInterface $paymentMethodData
     * @return int Order ID
     * @throws LocalizedException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function aroundSavePaymentInformationAndPlaceOrder(
        PaymentInformationManagement $subject,
        callable $proceed,
        $cartId,
        $email,
        PaymentDetailsInterface $paymentMethodData
    ) {
        $paymentMethod = $paymentMethodData->getPaymentMethod();
        if (!isset($paymentMethod['method']) || $paymentMethod['method'] !== self::CODE) {
            return $proceed($cartId, $email, $paymentMethodData);
        }

        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->checkoutSession->getQuote();
        if (!$quote->getId() || !$quote->getIsActive()) {
            throw new LocalizedException(__('Invalid quote.'));
        }

        $storeId = $quote->getStoreId();
        $isActive = $this->scopeConfig->getValue('payment/p2b2_checkout/active', ScopeInterface::SCOPE_STORE, $storeId);
        if (!$isActive) {
            throw new LocalizedException(__('Payment method is not available.'));
        }

        $environment = $this->scopeConfig->getValue('payment/p2b2_checkout/environment', ScopeInterface::SCOPE_STORE, $storeId);
        $isSandbox = $environment === 'sandbox';
        $clientKey = $isSandbox
            ? $this->scopeConfig->getValue('payment/p2b2_checkout/client_key_sandbox', ScopeInterface::SCOPE_STORE, $storeId)
            : $this->scopeConfig->getValue('payment/p2b2_checkout/client_key_production', ScopeInterface::SCOPE_STORE, $storeId);
        $clientSecret = $isSandbox
            ? $this->scopeConfig->getValue('payment/p2b2_checkout/client_secret_sandbox', ScopeInterface::SCOPE_STORE, $storeId)
            : $this->scopeConfig->getValue('payment/p2b2_checkout/client_secret_production', ScopeInterface::SCOPE_STORE, $storeId);
        $apiEndpoint = $this->scopeConfig->getValue('payment/p2b2_checkout/api_endpoint', ScopeInterface::SCOPE_STORE, $storeId);
        $apiKey = $this->scopeConfig->getValue('payment/p2b2_checkout/api_key', ScopeInterface::SCOPE_STORE, $storeId);
        $merchantName = $this->scopeConfig->getValue('payment/p2b2_checkout/merchant_name', ScopeInterface::SCOPE_STORE, $storeId);
        $merchantCallbackUrl = $this->scopeConfig->getValue('payment/p2b2_checkout/merchant_callback_url', ScopeInterface::SCOPE_STORE, $storeId);
        $beneficiaryName = $this->scopeConfig->getValue('payment/p2b2_checkout/beneficiary_name', ScopeInterface::SCOPE_STORE, $storeId);
        $beneficiaryIban = $this->scopeConfig->getValue('payment/p2b2_checkout/beneficiary_iban', ScopeInterface::SCOPE_STORE, $storeId);
        $debugMode = $this->scopeConfig->getValue('payment/p2b2_checkout/debug_mode', ScopeInterface::SCOPE_STORE, $storeId);

        if (empty($clientKey) || empty($clientSecret) || empty($apiKey) || empty($merchantName) || empty($merchantCallbackUrl) || empty($beneficiaryName) || empty($beneficiaryIban)) {
            throw new LocalizedException(__('Payment configuration is incomplete.'));
        }

        $body = [
            'clientKey' => $clientKey,
            'clientSecret' => $clientSecret,
            'remittanceInformationUnstructured' => 'Order ' . $quote->getReservedOrderId(),
            'amount' => number_format($quote->getGrandTotal(), 2, '.', ''),
            'currency' => $quote->getQuoteCurrencyCode(),
            'beneficiaryName' => $beneficiaryName,
            'IBANBen' => $beneficiaryIban,
            'merchantName' => $merchantName,
            'merchantCallbackUrl' => $merchantCallbackUrl,
            'localInstrument' => 'SCT-Inst',
            'isSandbox' => $isSandbox ? 'TRUE' : 'FALSE'
        ];

        $jsonBody = json_encode($body);
        if ($debugMode) {
            $this->p2b2Logger->info('Creating payment session', ['request' => $jsonBody, 'quote_id' => $quote->getId()]);
        }

        $this->httpClient->setHeaders([
            'Content-Type' => 'application/json',
            'x-api-key' => $apiKey
        ]);
        $this->httpClient->post($apiEndpoint, $jsonBody);
        $responseBody = $this->httpClient->getBody();

        if ($debugMode) {
            $this->p2b2Logger->info('Payment session response', ['response' => $responseBody, 'quote_id' => $quote->getId()]);
        }

        $responseData = json_decode($responseBody, true);
        if (json_last_error() !== JSON_ERROR_NONE || !isset($responseData['pb2bId'])) {
            $this->p2b2Logger->error('Failed to create payment session', ['error' => $responseBody, 'quote_id' => $quote->getId()]);
            throw new LocalizedException(__('Unable to create payment session. Please try again.'));
        }

        $pb2bId = $responseData['pb2bId'];

        // Add pb2bId to additional data
        $additionalData = $paymentMethod['additional_data'] ?? [];
        $additionalData['pb2b_id'] = $pb2bId;
        $paymentMethod['additional_data'] = $additionalData;
        $paymentMethodData->setPaymentMethod($paymentMethod);

        // Proceed with order placement (order will be pending)
        $orderId = $proceed($cartId, $email, $paymentMethodData);

        $this->p2b2Logger->info('Payment session created and order placed', ['pb2b_id' => $pb2bId, 'order_id' => $orderId]);

        return $orderId;
    }
}