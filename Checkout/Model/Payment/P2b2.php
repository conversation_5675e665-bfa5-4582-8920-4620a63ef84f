<?php
namespace P2b2_Checkout\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Payment\Model\InfoInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class P2b2 extends AbstractMethod
{
    const CODE = 'p2b2_checkout';

    protected $_code = self::CODE;
    protected $_isOffline = false;
    protected $_canAuthorize = true;
    protected $_canCapture = true;
    protected $_canRefund = false;
    protected $_canVoid = false;
    protected $_canUseInternal = true;
    protected $_canFetchTransactionInfo = false;
    protected $_supportsVault = false;
    protected $_formBlockType = \P2b2_Checkout\Block\Form\P2b2::class;
    protected $_infoBlockType = 'Magento\Payment\Block\Info';

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param ScopeConfigInterface $scopeConfig
     * @param \Magento\Payment\Helper\Data $paymentData
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Payment\Model\Method\Logger $logger
     * @param \Magento\Framework\Module\ModuleListInterface $moduleList
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate
     * @param array $data
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory,
        \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory,
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Payment\Model\Method\Logger $logger,
        \Magento\Framework\Module\ModuleListInterface $moduleList,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $paymentData,
            $scopeConfig,
            $logger,
            $moduleList,
            $localeDate,
            $data
        );
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function getOrderPlaceRedirectUrl()
    {
        try {
            /** @var InfoInterface $paymentInfo */
            $paymentInfo = $this->getInfoInstance();
            $pb2bId = $paymentInfo->getAdditionalInformation('pb2b_id');
            if (empty($pb2bId)) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Payment session not found.'));
            }
            return 'https://session.pay-by-bank.co.uk/?pb2bid=' . urlencode($pb2bId);
        } catch (\Exception $e) {
            $this->_logger->critical($e);
            // Fallback to default if any issue
            return parent::getOrderPlaceRedirectUrl();
        }
    }

    /**
     * Availability for currency
     *
     * @param string $currencyCode
     * @return bool
     */
    public function canUseForCurrency($currencyCode)
    {
        return true; // Support all currencies as per spec, but adjust if needed
    }
}