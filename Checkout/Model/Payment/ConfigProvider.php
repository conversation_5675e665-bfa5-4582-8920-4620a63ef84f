<?php
namespace P2b2_Checkout\Model\Payment;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Payment\Model\MethodList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider implements ConfigProviderInterface
{
    const CODE = \P2b2_Checkout\Model\Payment\P2b2::CODE;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfig()
    {
        $config = [];
        if (!$this->isActive()) {
            return $config;
        }

        $config['payment'][self::CODE] = [
            'title' => $this->scopeConfig->getValue(
                'payment/p2b2_checkout/title',
                ScopeInterface::SCOPE_STORE
            )
        ];

        return $config;
    }

    /**
     * @return bool
     */
    protected function isActive()
    {
        return (bool) $this->scopeConfig->getValue(
            'payment/p2b2_checkout/active',
            ScopeInterface::SCOPE_STORE
        );
    }
}